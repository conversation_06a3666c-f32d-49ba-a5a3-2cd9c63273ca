import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { PDFService } from './services/pdf.ts';
import { GeminiService } from './services/gemini.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type { AnalysisResult, ColumnDefinition } from './types.ts';

export class AnalyzeUtility {
  private config: Config;
  private pdfService: PDFService;
  private geminiService: GeminiService;

  constructor() {
    this.config = {} as Config; // Will be initialized in init()
    this.pdfService = {} as PDFService; // Will be initialized in init()
    this.geminiService = {} as GeminiService; // Will be initialized in init()
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Analyze Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const geminiConfig = this.config.getGeminiConfig();
    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.pdfService = new PDFService(processingConfig.pdfPath, processingConfig.tempDir);
    this.geminiService = new GeminiService(geminiConfig);

    await this.pdfService.init();

    await logger.success('Analyze Utility initialized successfully');
  }

  async analyze(): Promise<AnalysisResult> {
    try {
      await logger.info('Starting column analysis process');

      const processingConfig = this.config.getProcessingConfig();
      const totalPages = await this.pdfService.getTotalPages();

      // Load existing analysis if available
      const existingAnalysis = await this.getLatestAnalysis();
      if (existingAnalysis) {
        await logger.info(`Found existing analysis with ${existingAnalysis.columns.length} columns. Making analysis additive.`);
      }

      // Get random sample pages
      const samplePages = this.pdfService.getRandomPageNumbers(processingConfig.samplePages);
      await logger.info(`Analyzing ${samplePages.length} random pages: ${samplePages.join(', ')}`);

      // Convert sample pages to images
      await logger.progress('Converting sample pages to images');
      const pageInfos = await this.pdfService.convertPagesToImages(samplePages);

      if (pageInfos.length === 0) {
        throw new Error('No pages were successfully converted to images');
      }

      // Extract image paths
      const imagePaths = pageInfos.map(info => info.imagePath);

      // Analyze columns using Gemini (additive approach)
      await logger.progress('Analyzing images with Gemini AI');
      const columns = await this.geminiService.analyzeColumnsFromImages(
        imagePaths,
        existingAnalysis?.columns
      );

      // Ensure required columns are present
      const enhancedColumns = this.ensureRequiredColumns(columns);

      // Calculate confidence based on consistency across pages
      const confidence = this.calculateAnalysisConfidence(enhancedColumns, pageInfos.length);

      // Create analysis result
      const analysisResult: AnalysisResult = {
        timestamp: new Date().toISOString(),
        pagesAnalyzed: samplePages,
        totalPages,
        columns: enhancedColumns,
        confidence,
        notes: existingAnalysis
          ? `Additive analysis based on ${pageInfos.length} random pages from ${totalPages} total pages. Enhanced existing ${existingAnalysis.columns.length} columns.`
          : `Analysis based on ${pageInfos.length} random pages from ${totalPages} total pages`,
      };

      // Save analysis result
      await this.saveAnalysisResult(analysisResult);

      await logger.success(`Column analysis completed. Identified ${enhancedColumns.length} columns with ${(confidence * 100).toFixed(1)}% confidence`);

      return analysisResult;
    } catch (error) {
      await logger.error('Column analysis failed', error);
      throw error;
    } finally {
      // Cleanup temporary files
      await this.pdfService.cleanup();
    }
  }

  private ensureRequiredColumns(columns: ColumnDefinition[]): ColumnDefinition[] {
    const requiredColumns: ColumnDefinition[] = [
      // Company name - ALWAYS required
      {
        name: 'Company_Name',
        description: 'Business or company name - appears in bold on the left-most part of each listing',
        dataType: 'string',
        required: true,
        examples: ['ABC Corporation', 'Smith & Associates', 'Green Valley Farms', 'Tech Solutions Inc.']
      },
      // Geographic columns - ALL required
      {
        name: 'City',
        description: 'City extracted from addresses',
        dataType: 'string',
        required: true,
        examples: ['Los Angeles', 'Toronto', 'Berlin', 'Amsterdam', 'Sydney']
      },
      {
        name: 'State_Province',
        description: 'State or Province extracted from addresses',
        dataType: 'string',
        required: true,
        examples: ['California', 'Ontario', 'Texas', 'Bavaria', 'Queensland']
      },
      {
        name: 'Country',
        description: 'Country extracted from the main address or any other address field',
        dataType: 'string',
        required: true,
        examples: ['USA', 'Canada', 'United Kingdom', 'Germany', 'Netherlands']
      },
      // Social media columns
      {
        name: 'Twitter_X',
        description: 'Twitter/X social media handle or URL',
        dataType: 'url',
        required: false,
        examples: ['@company_name', 'https://twitter.com/company', 'https://x.com/company']
      },
      {
        name: 'Facebook',
        description: 'Facebook page URL or handle',
        dataType: 'url',
        required: false,
        examples: ['https://facebook.com/company', 'facebook.com/company', '@company']
      },
      {
        name: 'LinkedIn',
        description: 'LinkedIn company page URL',
        dataType: 'url',
        required: false,
        examples: ['https://linkedin.com/company/company-name', 'linkedin.com/in/person']
      },
      {
        name: 'Instagram',
        description: 'Instagram account handle or URL',
        dataType: 'url',
        required: false,
        examples: ['@company_name', 'https://instagram.com/company', 'instagram.com/company']
      },
      {
        name: 'YouTube',
        description: 'YouTube channel URL',
        dataType: 'url',
        required: false,
        examples: ['https://youtube.com/c/company', 'youtube.com/@company']
      },
      {
        name: 'Other_Social_Media',
        description: 'Other social media accounts not covered by specific columns (TikTok, Pinterest, etc.)',
        dataType: 'string',
        required: false,
        examples: ['TikTok: @company', 'Pinterest: pinterest.com/company', 'WhatsApp: +**********']
      }
    ];

    const enhancedColumns = [...columns];
    const addedColumns: string[] = [];

    // Check and add each required column if not already present
    for (const requiredCol of requiredColumns) {
      const exists = columns.some(col =>
        this.isColumnSimilar(col, requiredCol)
      );

      if (!exists) {
        enhancedColumns.push(requiredCol);
        addedColumns.push(requiredCol.name);
      }
    }

    if (addedColumns.length > 0) {
      logger.info(`Added required columns: ${addedColumns.join(', ')}`);
    }

    return enhancedColumns;
  }

  private isColumnSimilar(existingCol: ColumnDefinition, requiredCol: ColumnDefinition): boolean {
    const existingName = existingCol.name.toLowerCase();
    const existingDesc = existingCol.description.toLowerCase();
    const requiredName = requiredCol.name.toLowerCase();

    // Check for company name columns
    if (requiredName.includes('company') && requiredName.includes('name')) {
      return existingName.includes('company') || existingName.includes('business') ||
        existingName.includes('name') || existingDesc.includes('company') ||
        existingDesc.includes('business') || existingDesc.includes('name');
    }

    // Check for geographic columns
    if (requiredName.includes('city')) {
      return existingName.includes('city') || existingDesc.includes('city');
    }
    if (requiredName.includes('state') || requiredName.includes('province')) {
      return existingName.includes('state') || existingName.includes('province') ||
        existingDesc.includes('state') || existingDesc.includes('province');
    }
    if (requiredName.includes('country')) {
      return existingName.includes('country') || existingDesc.includes('country');
    }

    // Check for social media columns
    if (requiredName.includes('twitter') || requiredName.includes('x')) {
      return existingName.includes('twitter') || existingName.includes('x') ||
        existingDesc.includes('twitter') || existingDesc.includes('x');
    }
    if (requiredName.includes('facebook')) {
      return existingName.includes('facebook') || existingDesc.includes('facebook');
    }
    if (requiredName.includes('linkedin')) {
      return existingName.includes('linkedin') || existingDesc.includes('linkedin');
    }
    if (requiredName.includes('instagram')) {
      return existingName.includes('instagram') || existingDesc.includes('instagram');
    }
    if (requiredName.includes('youtube')) {
      return existingName.includes('youtube') || existingDesc.includes('youtube');
    }

    return false;
  }

  private calculateAnalysisConfidence(columns: ColumnDefinition[], pagesAnalyzed: number): number {
    // Base confidence on number of columns found and pages analyzed
    let confidence = 0.7; // Base confidence

    // Increase confidence based on number of columns found
    if (columns.length >= 8) confidence += 0.15;
    else if (columns.length >= 5) confidence += 0.1;
    else if (columns.length >= 3) confidence += 0.05;

    // Increase confidence based on number of pages analyzed
    if (pagesAnalyzed >= 5) confidence += 0.1;
    else if (pagesAnalyzed >= 3) confidence += 0.05;

    // Check for expected core columns
    const coreColumns = ['business_name', 'name', 'company', 'phone', 'email', 'address'];
    const foundCoreColumns = columns.filter(col =>
      coreColumns.some(core => col.name.toLowerCase().includes(core))
    );

    // Check for required geographic columns
    const requiredGeoColumns = ['city', 'state', 'province', 'country'];
    const foundGeoColumns = columns.filter(col =>
      requiredGeoColumns.some(geo => col.name.toLowerCase().includes(geo))
    );

    // Check for company name (critical requirement)
    const hasCompanyName = columns.some(col =>
      col.name.toLowerCase().includes('company') ||
      col.name.toLowerCase().includes('business') ||
      (col.name.toLowerCase().includes('name') && !col.name.toLowerCase().includes('contact'))
    );

    if (foundCoreColumns.length >= 4) confidence += 0.1;
    else if (foundCoreColumns.length >= 2) confidence += 0.05;

    if (foundGeoColumns.length >= 3) confidence += 0.1;
    else if (foundGeoColumns.length >= 2) confidence += 0.05;

    if (hasCompanyName) confidence += 0.05;

    return Math.min(confidence, 1.0);
  }

  private async saveAnalysisResult(result: AnalysisResult): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const analysisDir = join(processingConfig.outputDir, 'analysis');
    await FileUtils.ensureDirectoryExists(analysisDir);

    const filename = FileUtils.generateTimestampedFilename('column_analysis', 'json');
    const filePath = join(analysisDir, filename);

    await FileUtils.writeJsonFile(filePath, result);
    await logger.info(`Analysis result saved to: ${filePath}`);

    // Also save a latest.json for easy access
    const latestPath = join(analysisDir, 'latest.json');
    await FileUtils.writeJsonFile(latestPath, result);
    await logger.debug('Latest analysis result saved');
  }

  async getLatestAnalysis(): Promise<AnalysisResult | null> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const latestPath = join(processingConfig.outputDir, 'analysis', 'latest.json');

      if (await FileUtils.fileExists(latestPath)) {
        return await FileUtils.readJsonFile<AnalysisResult>(latestPath);
      }

      return null;
    } catch (error) {
      await logger.warn('Failed to load latest analysis', error);
      return null;
    }
  }

  async listAnalysisResults(): Promise<string[]> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const analysisDir = join(processingConfig.outputDir, 'analysis');

      if (await FileUtils.fileExists(analysisDir)) {
        return await FileUtils.listFiles(analysisDir, '.json');
      }

      return [];
    } catch (error) {
      await logger.warn('Failed to list analysis results', error);
      return [];
    }
  }
}

// CLI function for direct usage
export async function runAnalysis(): Promise<void> {
  const analyzer = new AnalyzeUtility();

  try {
    await analyzer.init();
    const result = await analyzer.analyze();

    console.log('\n📊 Analysis Results:');
    console.log(`📄 Pages analyzed: ${result.pagesAnalyzed.join(', ')}`);
    console.log(`📋 Columns identified: ${result.columns.length}`);
    console.log(`🎯 Confidence: ${(result.confidence * 100).toFixed(1)}%`);
    console.log('\n📝 Identified Columns:');

    result.columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.name} (${col.dataType}) - ${col.description}`);
      if (col.examples && col.examples.length > 0) {
        console.log(`   Examples: ${col.examples.join(', ')}`);
      }
    });

  } catch (error) {
    console.error('❌ Analysis failed:', error);
    Deno.exit(1);
  }
}
