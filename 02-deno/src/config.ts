import { load } from '@std/dotenv/mod.ts';
import type { GeminiConfig, ProcessingConfig } from './types.ts';
import { logger } from './utils/logger.ts';

export class Config {
  private static instance: Config;
  private env: Record<string, string> = {};

  private constructor() { }

  static async getInstance(): Promise<Config> {
    if (!Config.instance) {
      Config.instance = new Config();
      await Config.instance.loadEnv();
    }
    return Config.instance;
  }

  private async loadEnv(): Promise<void> {
    try {
      this.env = await load();
      await logger.debug('Environment variables loaded');
    } catch (error) {
      await logger.warn('Failed to load .env file, using system environment variables', error);
      // Fallback to system environment variables
      for (const [key, value] of Object.entries(Deno.env.toObject())) {
        this.env[key] = value;
      }
    }
  }

  private getEnvVar(key: string, defaultValue?: string): string {
    const value = this.env[key] || Deno.env.get(key) || defaultValue;
    if (!value) {
      throw new Error(`Required environment variable ${key} is not set`);
    }
    return value;
  }

  private getEnvVarOptional(key: string, defaultValue: string): string {
    return this.env[key] || Deno.env.get(key) || defaultValue;
  }

  getGeminiConfig(): GeminiConfig {
    return {
      apiKey: this.getEnvVar('GEMINI_API_KEY'),
      model: this.getEnvVarOptional('GEMINI_MODEL', 'gemini-1.5-flash'),
      temperature: parseFloat(this.getEnvVarOptional('GEMINI_TEMPERATURE', '0.1')),
      maxTokens: parseInt(this.getEnvVarOptional('GEMINI_MAX_TOKENS', '8192'), 10),
    };
  }

  getProcessingConfig(): ProcessingConfig {
    return {
      pdfPath: this.getEnvVarOptional('PDF_PATH', './pdf/list.pdf'),
      outputDir: this.getEnvVarOptional('OUTPUT_DIR', './output'),
      tempDir: this.getEnvVarOptional('TEMP_DIR', './temp'),
      logsDir: this.getEnvVarOptional('LOGS_DIR', './logs'),
      samplePages: parseInt(this.getEnvVarOptional('SAMPLE_PAGES', '5'), 10),
      maxPagesPerBatch: parseInt(this.getEnvVarOptional('MAX_PAGES_PER_BATCH', '10'), 10),
    };
  }

  getExcelConfig() {
    return {
      filename: this.getEnvVarOptional('EXCEL_FILENAME', 'business_listings.xlsx'),
      sheetName: this.getEnvVarOptional('EXCEL_SHEET_NAME', 'Business Listings'),
    };
  }

  isDevelopment(): boolean {
    return this.getEnvVarOptional('DENO_ENV', 'development') === 'development';
  }

  isDebugEnabled(): boolean {
    return this.getEnvVarOptional('DEBUG', 'false') === 'true';
  }

  async validateConfig(): Promise<void> {
    try {
      const geminiConfig = this.getGeminiConfig();
      const processingConfig = this.getProcessingConfig();

      // Validate Gemini API key
      if (!geminiConfig.apiKey || geminiConfig.apiKey === 'your_gemini_api_key_here') {
        throw new Error('GEMINI_API_KEY must be set to a valid API key');
      }

      // Validate PDF path exists
      try {
        await Deno.stat(processingConfig.pdfPath);
      } catch {
        throw new Error(`PDF file not found: ${processingConfig.pdfPath}`);
      }

      // Validate numeric values
      if (geminiConfig.temperature < 0 || geminiConfig.temperature > 2) {
        throw new Error('GEMINI_TEMPERATURE must be between 0 and 2');
      }

      if (processingConfig.samplePages < 1) {
        throw new Error('SAMPLE_PAGES must be at least 1');
      }

      if (processingConfig.maxPagesPerBatch < 1) {
        throw new Error('MAX_PAGES_PER_BATCH must be at least 1');
      }

      await logger.info('Configuration validation passed');
    } catch (error) {
      await logger.error('Configuration validation failed', error);
      throw error;
    }
  }
}
