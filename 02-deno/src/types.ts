// Core types for the OCR application

export interface ColumnDefinition {
  name: string;
  description: string;
  dataType: 'string' | 'number' | 'email' | 'phone' | 'url' | 'address';
  required: boolean;
  examples?: string[];
}

export interface AnalysisResult {
  timestamp: string;
  pagesAnalyzed: number[];
  totalPages: number;
  columns: ColumnDefinition[];
  confidence: number;
  notes?: string;
}

export interface CategoryInfo {
  name: string;
  startPage: number;
  endPage?: number;
  color?: string;
  description?: string;
}

export interface CategoryMapping {
  [pageNumber: number]: {
    categoryName: string;
    color?: string;
    description?: string;
    confidence: number;
  };
}

export interface BusinessListing {
  pageNumber: number;
  entryNumber: number;
  data: Record<string, string | number | null>;
  confidence: number;
  rawText?: string;
  spanMultiplePages?: boolean;
  continuedFromPage?: number;
  continuesOnPage?: number;
  category?: string;
  country?: string;
  // Geographic data
  city?: string;
  stateProvince?: string;
  // Social media data
  twitterX?: string;
  facebook?: string;
  linkedin?: string;
  instagram?: string;
  youtube?: string;
  otherSocialMedia?: string;
}

export interface ExtractionResult {
  timestamp: string;
  pageNumber: number;
  totalEntries: number;
  listings: BusinessListing[];
  processingTime: number;
  errors?: string[];
  category?: string;
}

export interface PageRange {
  startPage: number;
  endPage: number;
}

export interface ExtractionOptions {
  pageRange?: PageRange;
  batchSize?: number;
  cumulativeResults?: boolean;
}

export interface CombinedResult {
  timestamp: string;
  totalListings: number;
  totalPages: number;
  columns: ColumnDefinition[];
  listings: BusinessListing[];
  statistics: {
    averageEntriesPerPage: number;
    pagesWithMultipleEntries: number;
    entriesSpanningPages: number;
    completenessScore: number;
  };
}

export interface GeminiConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface ProcessingConfig {
  pdfPath: string;
  outputDir: string;
  tempDir: string;
  logsDir: string;
  samplePages: number;
  maxPagesPerBatch: number;
}

export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: unknown;
}

export interface PDFPageInfo {
  pageNumber: number;
  imagePath: string;
  width: number;
  height: number;
  fileSize: number;
}

export interface ExcelExportOptions {
  filename: string;
  sheetName: string;
  includeMetadata: boolean;
  includeStatistics: boolean;
  autoFitColumns: boolean;
}
