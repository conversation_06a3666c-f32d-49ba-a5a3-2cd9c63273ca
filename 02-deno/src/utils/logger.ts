import { ensureDir } from '@std/fs/mod.ts';
import type { LogEntry } from '../types.ts';

export class Logger {
  private logsDir: string;
  private logFile: string;

  constructor(logsDir = './logs') {
    this.logsDir = logsDir;
    this.logFile = `${logsDir}/app-${new Date().toISOString().split('T')[0]}.log`;
  }

  async init(): Promise<void> {
    await ensureDir(this.logsDir);
  }

  private async writeLog(entry: LogEntry): Promise<void> {
    const logLine = `${entry.timestamp} [${entry.level.toUpperCase()}] ${entry.message}${entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : ''
      }\n`;

    try {
      await Deno.writeTextFile(this.logFile, logLine, { append: true });
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private createLogEntry(level: LogEntry['level'], message: string, data?: unknown): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    };
  }

  async info(message: string, data?: unknown): Promise<void> {
    const entry = this.createLogEntry('info', message, data);
    console.log(`ℹ️  ${message}`);
    await this.writeLog(entry);
  }

  async warn(message: string, data?: unknown): Promise<void> {
    const entry = this.createLogEntry('warn', message, data);
    console.warn(`⚠️  ${message}`);
    await this.writeLog(entry);
  }

  async error(message: string, data?: unknown): Promise<void> {
    const entry = this.createLogEntry('error', message, data);
    console.error(`❌ ${message}`);
    await this.writeLog(entry);
  }

  async debug(message: string, data?: unknown): Promise<void> {
    const entry = this.createLogEntry('debug', message, data);
    if (Deno.env.get('DEBUG') === 'true') {
      console.log(`🐛 ${message}`);
    }
    await this.writeLog(entry);
  }

  async success(message: string, data?: unknown): Promise<void> {
    const entry = this.createLogEntry('info', `SUCCESS: ${message}`, data);
    console.log(`✅ ${message}`);
    await this.writeLog(entry);
  }

  async progress(message: string, current?: number, total?: number): Promise<void> {
    const progressText = current !== undefined && total !== undefined
      ? ` (${current}/${total})`
      : '';
    const entry = this.createLogEntry('info', `PROGRESS: ${message}${progressText}`);
    console.log(`🔄 ${message}${progressText}`);
    await this.writeLog(entry);
  }
}

// Global logger instance
export const logger = new Logger();
