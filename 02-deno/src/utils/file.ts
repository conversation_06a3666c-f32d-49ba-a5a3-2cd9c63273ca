import { ensureDir, exists } from '@std/fs/mod.ts';
import { dirname, join } from '@std/path/mod.ts';
import { logger } from './logger.ts';

export class FileUtils {
  static async ensureDirectoryExists(path: string): Promise<void> {
    try {
      await ensureDir(path);
    } catch (error) {
      await logger.error(`Failed to create directory: ${path}`, error);
      throw error;
    }
  }

  static async fileExists(path: string): Promise<boolean> {
    try {
      return await exists(path);
    } catch {
      return false;
    }
  }

  static async readJsonFile<T>(path: string): Promise<T> {
    try {
      const content = await Deno.readTextFile(path);
      return JSON.parse(content) as T;
    } catch (error) {
      await logger.error(`Failed to read JSON file: ${path}`, error);
      throw error;
    }
  }

  static async writeJsonFile(path: string, data: unknown): Promise<void> {
    try {
      await this.ensureDirectoryExists(dirname(path));
      const content = JSON.stringify(data, null, 2);
      await Deno.writeTextFile(path, content);
      await logger.debug(`JSON file written: ${path}`);
    } catch (error) {
      await logger.error(`Failed to write JSON file: ${path}`, error);
      throw error;
    }
  }

  static async copyFile(source: string, destination: string): Promise<void> {
    try {
      await this.ensureDirectoryExists(dirname(destination));
      await Deno.copyFile(source, destination);
      await logger.debug(`File copied: ${source} -> ${destination}`);
    } catch (error) {
      await logger.error(`Failed to copy file: ${source} -> ${destination}`, error);
      throw error;
    }
  }

  static async deleteFile(path: string): Promise<void> {
    try {
      if (await this.fileExists(path)) {
        await Deno.remove(path);
        await logger.debug(`File deleted: ${path}`);
      }
    } catch (error) {
      await logger.error(`Failed to delete file: ${path}`, error);
      throw error;
    }
  }

  static async listFiles(directory: string, extension?: string): Promise<string[]> {
    try {
      const files: string[] = [];
      for await (const entry of Deno.readDir(directory)) {
        if (entry.isFile) {
          if (!extension || entry.name.endsWith(extension)) {
            files.push(join(directory, entry.name));
          }
        }
      }
      return files.sort();
    } catch (error) {
      await logger.error(`Failed to list files in directory: ${directory}`, error);
      throw error;
    }
  }

  static async getFileSize(path: string): Promise<number> {
    try {
      const stat = await Deno.stat(path);
      return stat.size;
    } catch (error) {
      await logger.error(`Failed to get file size: ${path}`, error);
      throw error;
    }
  }

  static generateTimestampedFilename(baseName: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${baseName}_${timestamp}.${extension}`;
  }

  static async cleanupTempFiles(tempDir: string, olderThanHours = 24): Promise<void> {
    try {
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);

      for await (const entry of Deno.readDir(tempDir)) {
        if (entry.isFile) {
          const filePath = join(tempDir, entry.name);
          const stat = await Deno.stat(filePath);

          if (stat.mtime && stat.mtime.getTime() < cutoffTime) {
            await this.deleteFile(filePath);
            await logger.debug(`Cleaned up old temp file: ${filePath}`);
          }
        }
      }
    } catch (error) {
      await logger.warn(`Failed to cleanup temp files in: ${tempDir}`, error);
    }
  }
}
