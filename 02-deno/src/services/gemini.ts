import { GoogleGenerativeAI, type GenerativeModel } from '@google/generative-ai';
import type { GeminiConfig, ColumnDefinition, BusinessListing, CategoryInfo } from '../types.ts';
import { logger } from '../utils/logger.ts';
import { encodeBase64 } from '@std/encoding/base64.ts';

export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(config: GeminiConfig) {
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: config.model,
      generationConfig: {
        temperature: config.temperature,
        maxOutputTokens: config.maxTokens,
      },
    });
  }

  async analyzeColumnsFromImages(imagePaths: string[], existingColumns?: ColumnDefinition[]): Promise<ColumnDefinition[]> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await logger.info(`Analyzing ${imagePaths.length} images for column structure (attempt ${attempt}/${maxRetries})`);

        // Build existing columns context
        const existingColumnsContext = existingColumns && existingColumns.length > 0
          ? `\n\nEXISTING COLUMNS FROM PREVIOUS ANALYSIS:\n${existingColumns.map(col =>
            `- ${col.name} (${col.dataType}): ${col.description}${col.required ? ' [REQUIRED]' : ''}`
          ).join('\n')}\n\nPlease ENHANCE and ADD TO these existing columns rather than replacing them. Keep all existing columns and add any new ones you discover.`
          : '';

        const prompt = `
You are an expert at analyzing business directory listings. I'm providing you with images of pages from a business directory PDF.

${existingColumns && existingColumns.length > 0
            ? 'This is an ADDITIVE analysis - you should enhance the existing column definitions and add any new columns you discover.'
            : 'Please analyze these images and identify ALL possible data columns/fields that could be extracted from the business listings.'
          }

For each column you identify, provide:
1. A clear, descriptive name
2. A brief description of what the field contains
3. The data type (string, number, email, phone, url, address)
4. Whether it appears to be required/always present (true/false)
5. 2-3 example values if visible

${existingColumnsContext}

CRITICAL REQUIREMENTS:
1. COMPANY NAME: The business/company name is ALWAYS required and must never be blank. It appears in bold on the left-most part of each listing.

2. GEOGRAPHIC SEPARATION: Always break down addresses into separate columns:
   - Country (ALWAYS required)
   - State_Province (ALWAYS required)
   - City (ALWAYS required)

3. SOCIAL MEDIA SEPARATION: Create separate columns for each social media platform:
   - Twitter/X (handles or URLs)
   - Facebook (page URLs or handles)
   - LinkedIn (company/personal profile URLs)
   - Instagram (handles or URLs)
   - YouTube (channel URLs)
   - Other_Social_Media (for TikTok, Pinterest, WhatsApp, etc.)

4. DETAILED EXTRACTION: Look for category headers (often in colored banners) that group businesses

5. IDENTIFY ALL FIELDS: Look for any other fields such as:
   - Website URLs, Fax numbers, Years in business, Certifications
   - Service areas, Hours of operation, Specialties, Awards
   - Membership information, Contact person names, etc.

Return your response as a JSON array of objects with this structure:
{
  "name": "field_name",
  "description": "Description of the field",
  "dataType": "string|number|email|phone|url|address",
  "required": true|false,
  "examples": ["example1", "example2"]
}

${existingColumns && existingColumns.length > 0
            ? 'IMPORTANT: Include ALL existing columns in your response (enhanced if needed) plus any new columns you discover.'
            : 'Be thorough and identify every possible field you can see in the listings.'
          }
`;

        const imageParts = await Promise.all(
          imagePaths.map(async (imagePath) => {
            const imageData = await Deno.readFile(imagePath);
            // Convert Uint8Array to base64 safely using Deno's standard library
            const base64String = encodeBase64(imageData);
            return {
              inlineData: {
                data: base64String,
                mimeType: 'image/jpeg',
              },
            };
          })
        );

        const result = await this.model.generateContent([prompt, ...imageParts]);
        const response = result.response;
        const text = response.text();

        // Extract JSON from the response
        const jsonMatch = text.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in Gemini response');
        }

        const columns: ColumnDefinition[] = JSON.parse(jsonMatch[0]);

        await logger.success(`Identified ${columns.length} potential columns`);
        await logger.debug('Identified columns', columns);

        return columns;
      } catch (error) {
        lastError = error as Error;
        await logger.warn(`Attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt < maxRetries) {
          const delay = attempt * 2000; // Exponential backoff: 2s, 4s
          await logger.info(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    await logger.error('Failed to analyze columns from images after all retries', lastError);
    throw lastError || new Error('Failed to analyze columns after all retries');
  }

  async extractCategoriesFromImages(imagePaths: string[]): Promise<CategoryInfo[]> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await logger.info(`Extracting categories from ${imagePaths.length} images (attempt ${attempt}/${maxRetries})`);

        const prompt = `
You are an expert at analyzing business directory listings. I'm providing you with images of pages from a business directory PDF.

Please identify and extract the CATEGORY HEADERS that appear at the beginning of each section. These are typically:
- Displayed in colored headers or banners
- Indicate the type of businesses that follow (e.g., "Breeders", "Greenhouse Technique", "Crop Protection", etc.)
- Apply to all subsequent pages until a new category header appears

For each category you find, provide:
1. The category name/title
2. The page number where it starts
3. Any color information if visible
4. A brief description of what type of businesses this category contains

Return the results as a JSON array in this format:
[
  {
    "name": "Category Name",
    "startPage": 1,
    "color": "blue" (if visible),
    "description": "Brief description of the category"
  }
]

Focus on identifying the main section headers that categorize the business listings.
`;

        const imageParts = await Promise.all(
          imagePaths.map(async (imagePath) => {
            const imageData = await Deno.readFile(imagePath);
            const base64String = encodeBase64(imageData);
            return {
              inlineData: {
                data: base64String,
                mimeType: 'image/jpeg',
              },
            };
          })
        );

        const result = await this.model.generateContent([prompt, ...imageParts]);
        const response = result.response;
        const text = response.text();

        // Extract JSON from the response
        const jsonMatch = text.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          await logger.warn('No categories found in images');
          return [];
        }

        const categories: CategoryInfo[] = JSON.parse(jsonMatch[0]);

        await logger.success(`Identified ${categories.length} categories`);
        await logger.debug('Identified categories', categories);

        return categories;
      } catch (error) {
        lastError = error as Error;
        await logger.warn(`Category extraction attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt < maxRetries) {
          const delay = attempt * 2000;
          await logger.info(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    await logger.warn('Failed to extract categories after all retries, returning empty array', lastError);
    return [];
  }

  async detectCategoryFromImage(imagePath: string, pageNumber: number): Promise<{
    categoryName: string;
    color?: string;
    description?: string;
    confidence: number;
  } | null> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await logger.debug(`Detecting category from page ${pageNumber} (attempt ${attempt}/${maxRetries})`);

        const prompt = `
You are an expert at analyzing business directory listings. I'm providing you with an image of a single page from a business directory PDF.

Please look for CATEGORY HEADERS on this page. These are typically:
- Displayed in colored headers or banners at the top of sections
- Indicate the type of businesses that follow (e.g., "Breeders", "Greenhouse Technique", "Crop Protection", etc.)
- Usually appear in bold text with colored backgrounds (orange, green, blue, etc.)

If you find a category header on this page, return the result as JSON in this format:
{
  "categoryName": "Category Name",
  "color": "blue" (if visible),
  "description": "Brief description of what type of businesses this category contains",
  "confidence": 0.95
}

If no category header is found on this page, return null.

Focus only on identifying category headers that would apply to business listings on this and subsequent pages.
`;

        const imageData = await Deno.readFile(imagePath);
        const base64String = encodeBase64(imageData);
        const imagePart = {
          inlineData: {
            data: base64String,
            mimeType: 'image/jpeg',
          },
        };

        const result = await this.model.generateContent([prompt, imagePart]);
        const response = result.response;
        const text = response.text();

        // Check if response indicates no category found
        if (text.toLowerCase().includes('null') || text.toLowerCase().includes('no category')) {
          return null;
        }

        // Extract JSON from the response
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          return null;
        }

        const categoryInfo = JSON.parse(jsonMatch[0]);

        if (categoryInfo && categoryInfo.categoryName) {
          await logger.debug(`Detected category "${categoryInfo.categoryName}" on page ${pageNumber}`);
          return categoryInfo;
        }

        return null;
      } catch (error) {
        lastError = error as Error;
        await logger.warn(`Category detection attempt ${attempt} failed for page ${pageNumber}: ${(error as Error).message}`);

        if (attempt < maxRetries) {
          const delay = attempt * 1000;
          await logger.debug(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    await logger.debug(`No category detected on page ${pageNumber} after ${maxRetries} attempts`);
    return null;
  }

  async extractDataFromImage(
    imagePath: string,
    columns: ColumnDefinition[],
    pageNumber: number
  ): Promise<{ listings: BusinessListing[]; category?: { categoryName: string; color?: string; description?: string; confidence: number } }> {
    try {
      await logger.progress(`Extracting data from page ${pageNumber}`);

      const columnDescriptions = columns.map(col =>
        `- ${col.name} (${col.dataType}): ${col.description}`
      ).join('\n');

      const prompt = `
You are an expert at extracting structured data from business directory listings.

I'm providing you with an image of a page from a business directory. Please extract ALL business listings from this page AND detect any category headers.

COLUMNS TO EXTRACT:
${columnDescriptions}

INSTRUCTIONS FOR BUSINESS LISTINGS:
1. Extract every business listing you can find on this page
2. For each listing, extract data for all available columns
3. If a field is not visible/available, use null
4. Assign each listing a sequential entry number starting from 1
5. Provide a confidence score (0-1) for each extraction
6. Ignore ads, special callout boxes, and other elements EVEN if they contain business-like information. We only care about actual business listings that are always in black text with no background colors (just white page background).

CRITICAL EXTRACTION RULES:
6. COMPANY NAME: The business/company name appears in BOLD text on the left-most part of each listing in the first line, followed by the company's specialty in ITALIC text. ALWAYS!
   - ONLY extract the plain-text company name if you can see BOLD font text that clearly represents a company name in the left-most part of the first line of the listing immediately followed by italicized specialty text.
   - If there is NO BOLDED company name followed by italicized specialty text visible, set Company_Name to null (this indicates a continuation entry)
   - DO NOT CONFUSE social media listing, website links, or other non-bold text as the company name
   - NEVER infer, generate, or derive company names from email addresses, websites, or other data
   - NEVER use text that is not in bolded font as a company name
   - NEVER create your own company names if the listing does not have a bolded company name
   - If you see data but no bold company name, this is likely a continuation of the previous page's last entry

7. GEOGRAPHIC DATA: Always separate address components:
   - Extract Country, State_Province, and City into separate fields (ALL REQUIRED)
   - Don't combine them into a single address field
   - These fields must NEVER be null

8. SOCIAL MEDIA: Separate each platform into its own column:
    - Twitter_X: Twitter/X handles or URLs
    - Facebook: Facebook page URLs or handles
    - LinkedIn: LinkedIn profile/company URLs
    - Instagram: Instagram handles or URLs
    - YouTube: YouTube channel URLs
    - Other_Social_Media: Any other platforms (TikTok, Pinterest, WhatsApp, etc.)

9. MULTI-PAGE LISTINGS: If a business listing appears to be cut off at the bottom of the page (incomplete information, text cut off), set "spanMultiplePages" to true. If a listing appears to be a continuation from the previous page (starts mid-sentence, missing bolded company name followed by italicized speciality text), set "isContinuationFromPreviousPage" to true.

INSTRUCTIONS FOR CATEGORY DETECTION:
Also look for CATEGORY HEADERS on this page. These are typically:
- Displayed in colored headers or banners at the top of listings, below the fixed headers
- Indicate the type of businesses that follow (e.g., "Breeders", "Greenhouse Technique", "Crop Protection", etc.)
- Usually appear in bold text with colored backgrounds (orange, green, blue, etc.)

EXAMPLE OF CONTINUATION ENTRY (no bold company name):
If you see a listing with contact details, address, etc. but NO BOLD company name at the start, your output should look like this:
{
  "pageNumber": ${pageNumber},
  "entryNumber": 1,
  "data": {
    "Company_Name": null,
    "Phone": "+31 ***********",
    "Email": "<EMAIL>",
    "Website": "www.example.com"
  },
  "confidence": 0.8,
  "spanMultiplePages": false,
  "isContinuationFromPreviousPage": true,
  "continuesOnPage": null
}

Return the results as a JSON object in this format:
{
  "listings": [
    {
      "pageNumber": ${pageNumber},
      "entryNumber": 1,
      "data": {
        // All column data here
      },
      "confidence": 0.95,
      "spanMultiplePages": false,
      "isContinuationFromPreviousPage": false,
      "continuesOnPage": null
    }
  ],
  "category": {
    "categoryName": "Category Name",
    "color": "blue",
    "description": "Brief description of what type of businesses this category contains",
    "confidence": 0.95
  }
}

If no category header is found on this page, omit the "category" field or set it to null.

Be thorough and extract every listing you can see. If text is partially cut off or unclear, do your best to extract what you can and adjust the confidence score accordingly.
`;

      const imageData = await Deno.readFile(imagePath);
      // Convert Uint8Array to base64 safely using Deno's standard library
      const base64String = encodeBase64(imageData);
      const imagePart = {
        inlineData: {
          data: base64String,
          mimeType: 'image/jpeg',
        },
      };

      const result = await this.model.generateContent([prompt, imagePart]);
      const response = result.response;
      const text = response.text();

      // Extract JSON from the response - try object format first, then array format for backward compatibility
      let jsonMatch = text.match(/\{[\s\S]*\}/);
      let extractedData: { listings: BusinessListing[]; category?: { categoryName: string; color?: string; description?: string; confidence: number } };

      if (jsonMatch) {
        // New format with both listings and category
        const parsedData = JSON.parse(jsonMatch[0]);
        if (parsedData.listings && Array.isArray(parsedData.listings)) {
          extractedData = parsedData;
        } else {
          // Fallback: if it's just an object with listing data, wrap it
          extractedData = { listings: [parsedData] };
        }
      } else {
        // Fallback to old array format
        jsonMatch = text.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in Gemini response');
        }
        const listings: BusinessListing[] = JSON.parse(jsonMatch[0]);
        extractedData = { listings };
      }

      await logger.success(`Extracted ${extractedData.listings.length} listings from page ${pageNumber}`);
      if (extractedData.category) {
        await logger.debug(`Detected category "${extractedData.category.categoryName}" on page ${pageNumber}`);
      }

      return extractedData;
    } catch (error) {
      await logger.error(`Failed to extract data from page ${pageNumber}`, error);
      throw error;
    }
  }

  async processMultipleImages(
    imagePaths: string[],
    columns: ColumnDefinition[]
  ): Promise<BusinessListing[]> {
    const allListings: BusinessListing[] = [];

    for (let i = 0; i < imagePaths.length; i++) {
      try {
        const imagePath = imagePaths[i];
        if (!imagePath) {
          await logger.warn(`Skipping undefined image path at index ${i}`);
          continue;
        }

        const pageNumber = i + 1; // Assuming sequential processing

        const extractionResult = await this.extractDataFromImage(imagePath, columns, pageNumber);
        allListings.push(...extractionResult.listings);

        // Add a small delay to avoid rate limiting
        if (i < imagePaths.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        await logger.error(`Failed to process image ${i + 1}`, error);
        // Continue with other images
      }
    }

    return allListings;
  }

  validateAndCleanData(listings: BusinessListing[]): BusinessListing[] {
    // Basic data validation and cleaning
    return listings.map(listing => {
      // Validate company name - allow null for potential continuation entries
      const companyNameFields = ['Company_Name', 'company_name', 'business_name', 'name'];
      let companyName = null;

      for (const field of companyNameFields) {
        if (listing.data[field] && typeof listing.data[field] === 'string' && listing.data[field].trim()) {
          companyName = listing.data[field].trim();
          break;
        }
      }

      if (!companyName) {
        // If no company name found, this might be a continuation entry - don't mark as invalid
        console.log(`Info: Listing on page ${listing.pageNumber} entry ${listing.entryNumber} has no company name (possible continuation)`);
      }

      // Clean phone numbers
      if (listing.data.phone && typeof listing.data.phone === 'string') {
        listing.data.phone = listing.data.phone.replace(/[^\d\-\(\)\+\s]/g, '');
      }

      // Clean email addresses
      if (listing.data.email && typeof listing.data.email === 'string') {
        listing.data.email = listing.data.email.toLowerCase().trim();
      }

      // Clean URLs
      if (listing.data.website && typeof listing.data.website === 'string') {
        let url = listing.data.website.trim();
        if (url && !url.startsWith('http')) {
          url = 'https://' + url;
        }
        listing.data.website = url;
      }

      return listing;
    });
  }
}
