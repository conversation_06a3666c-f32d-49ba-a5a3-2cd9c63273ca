#!/usr/bin/env -S deno run --allow-all

// Simple test to verify the project setup
console.log('🧪 Testing Deno OCR Project Setup...\n');

try {
  // Test imports
  console.log('📦 Testing imports...');

  // Test standard library imports
  const { ensureDir } = await import('@std/fs/mod.ts');
  const { join } = await import('@std/path/mod.ts');
  console.log('✅ Standard library imports work');

  // Test npm imports (these might take a moment to download)
  console.log('📥 Testing npm package imports...');

  try {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    console.log('✅ Google Generative AI import works');
  } catch (error) {
    console.log('⚠️  Google Generative AI import failed (this is expected without network):', error instanceof Error ? error.message : String(error));
  }

  try {
    const ExcelJS = await import('exceljs');
    console.log('✅ ExcelJS import works');
  } catch (error) {
    console.log('⚠️  ExcelJS import failed (this is expected without network):', error instanceof Error ? error.message : String(error));
  }

  // Test project structure
  console.log('\n📁 Testing project structure...');

  const requiredDirs = ['src', 'src/services', 'src/utils'];
  for (const dir of requiredDirs) {
    try {
      const stat = await Deno.stat(dir);
      if (stat.isDirectory) {
        console.log(`✅ Directory exists: ${dir}`);
      }
    } catch {
      console.log(`❌ Directory missing: ${dir}`);
    }
  }

  const requiredFiles = [
    'deno.json',
    '.env.example',
    'src/main.ts',
    'src/config.ts',
    'src/types.ts',
    'src/analyze.ts',
    'src/extract.ts',
    'src/combine.ts'
  ];

  for (const file of requiredFiles) {
    try {
      const stat = await Deno.stat(file);
      if (stat.isFile) {
        console.log(`✅ File exists: ${file}`);
      }
    } catch {
      console.log(`❌ File missing: ${file}`);
    }
  }

  // Test TypeScript compilation
  console.log('\n🔍 Testing TypeScript compilation...');
  try {
    // This will check if the TypeScript files can be parsed
    const { Config } = await import('./src/config.ts');
    console.log('✅ TypeScript compilation works');
  } catch (error) {
    console.log('❌ TypeScript compilation failed:', error instanceof Error ? error.message : String(error));
  }

  console.log('\n🎉 Setup test completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Create .env file: cp .env.example .env');
  console.log('2. Add your GEMINI_API_KEY to .env');
  console.log('3. Place your PDF in pdf/list.pdf');
  console.log('4. Run: deno task start setup');

} catch (error) {
  console.error('❌ Setup test failed:', error);
  Deno.exit(1);
}
