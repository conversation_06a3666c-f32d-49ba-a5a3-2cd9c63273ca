{"name": "@guide-ocr/deno", "version": "1.0.0", "exports": "./src/main.ts", "tasks": {"setup-dirs": "deno run --allow-all setup.ts", "start": "deno run --allow-all src/main.ts", "dev": "deno run --allow-all --watch src/main.ts", "analyze": "deno run --allow-all src/main.ts analyze", "extract": "deno run --allow-all src/main.ts extract", "convert": "deno run --allow-all src/main.ts convert", "combine": "deno run --allow-all src/main.ts combine", "setup": "deno run --allow-all src/main.ts setup", "clean": "deno run --allow-all src/main.ts clean", "lint": "deno lint src/", "fmt": "deno fmt src/", "check": "deno check **/*.ts", "test": "deno test --allow-all src/"}, "imports": {"@std/": "https://deno.land/std@0.224.0/", "@google/generative-ai": "npm:@google/generative-ai@^0.24.1", "exceljs": "npm:exceljs@^4.4.0", "sharp": "npm:sharp@^0.34.2", "commander": "npm:commander@^14.0.0"}, "compilerOptions": {"lib": ["deno.window", "dom"], "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false}, "lint": {"rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": true, "proseWrap": "preserve"}, "exclude": ["temp/", "output/", "logs/", "node_modules/"]}