# Setup Guide - Guide OCR Deno Edition

This guide will help you set up and run the Guide OCR Deno application for processing PDF business listings with AI-powered OCR.

## Prerequisites

### 1. Install Deno

**macOS/Linux:**
```bash
curl -fsSL https://deno.land/install.sh | sh
```

**Windows:**
```powershell
irm https://deno.land/install.ps1 | iex
```

**Or using package managers:**
```bash
# macOS with Homebrew
brew install deno

# Ubuntu/Debian
sudo snap install deno

# Windows with Chocolatey
choco install deno
```

Verify installation:
```bash
deno --version
```

### 2. Install System Dependencies

**macOS:**
```bash
brew install poppler
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install poppler-utils
```

**Windows:**
Download and install poppler from: https://poppler.freedesktop.org/

### 3. Get Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for later use

## Project Setup

### 1. Navigate to Project Directory

```bash
cd 02-deno
```

### 2. Create Required Directories

```bash
# Run the setup script to create directories
deno run --allow-all setup.ts
```

Or manually create directories:
```bash
mkdir -p output/analysis output/extracted output/combined temp logs pdf
```

### 3. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file:
```bash
# Required: Your Google Gemini API key
GEMINI_API_KEY=your_actual_api_key_here

# Optional: Customize paths and settings
PDF_PATH=./pdf/list.pdf
OUTPUT_DIR=./output
TEMP_DIR=./temp
SAMPLE_PAGES=5
MAX_PAGES_PER_BATCH=10
```

### 4. Add Your PDF File

Place your PDF file in the `pdf/` directory:
```bash
# If you have the PDF in the parent directory
cp ../00-pdf/list.pdf pdf/list.pdf

# Or copy from wherever your PDF is located
cp /path/to/your/business-directory.pdf pdf/list.pdf
```

### 5. Verify Setup

Run the setup verification:
```bash
deno task start setup
```

This will:
- Validate your configuration
- Check if the PDF file exists
- Test the Gemini API connection
- Verify all dependencies

## Running the Application

### Option 1: Full Pipeline (Recommended)

Run the complete OCR pipeline in one command:

```bash
deno task start full
```

This will:
1. Analyze sample pages to identify data columns
2. Extract data from all pages
3. Combine results and export to Excel

### Option 2: Step-by-Step

#### Step 1: Analyze Columns
```bash
deno task analyze
# or with custom options
deno task start analyze --pages 10
```

#### Step 2: Extract Data
```bash
deno task extract
# or with custom batch size
deno task start extract --batch-size 5
```

#### Step 3: Combine and Export
```bash
deno task combine
# or with custom filename
deno task start combine --filename my_listings.xlsx
```

## Testing the Setup

Run a quick test to verify everything is working:

```bash
deno run --allow-all test-setup.ts
```

This will test:
- Import functionality
- Project structure
- TypeScript compilation
- File existence

## Troubleshooting

### Common Issues

#### 1. "Permission denied" errors
Make sure you're running commands with `--allow-all` flag or specific permissions:
```bash
deno run --allow-read --allow-write --allow-net --allow-env --allow-run src/main.ts
```

#### 2. "GEMINI_API_KEY not set"
- Ensure you've created the `.env` file
- Check that your API key is correctly set
- Verify there are no extra spaces or quotes

#### 3. "PDF file not found"
- Check the `PDF_PATH` in your `.env` file
- Ensure the PDF file exists at the specified location
- Verify file permissions

#### 4. "pdfinfo command not found"
Install poppler-utils:
```bash
# macOS
brew install poppler

# Ubuntu/Debian
sudo apt-get install poppler-utils
```

#### 5. Network/Import Issues
If you're having trouble with npm imports, try:
```bash
# Clear Deno cache
deno cache --reload src/main.ts

# Or run with specific permissions
deno run --allow-all --reload src/main.ts
```

#### 6. Memory Issues with Large PDFs
- Reduce `MAX_PAGES_PER_BATCH` in `.env`
- Process smaller sections at a time
- Ensure sufficient system memory

### Debug Mode

Enable detailed logging:
```bash
DEBUG=true deno task start [command]
```

### Check Logs

Application logs are saved in the `logs/` directory:
```bash
# View latest log
tail -f logs/app-$(date +%Y-%m-%d).log

# View all logs
ls -la logs/
```

## Performance Tips

1. **Batch Size**: Adjust `MAX_PAGES_PER_BATCH` based on your system memory
2. **Sample Pages**: Use fewer sample pages for faster analysis
3. **API Limits**: The application includes rate limiting for Gemini API
4. **Cleanup**: Run `deno task clean` periodically to remove temporary files

## File Structure After Setup

```
02-deno/
├── .env                   # Your configuration
├── pdf/list.pdf          # Your PDF file
├── output/               # Generated results
│   ├── analysis/         # Column analysis
│   ├── extracted/        # Page extractions
│   └── combined/         # Final Excel files
├── temp/                 # Temporary images
└── logs/                 # Application logs
```

## Next Steps

Once setup is complete:

1. **Run Analysis**: `deno task analyze`
2. **Extract Data**: `deno task extract`
3. **Export to Excel**: `deno task combine`
4. **Check Results**: Open the Excel file in `output/`

## Getting Help

If you encounter issues:

1. Check the logs in `logs/` directory
2. Run with debug mode: `DEBUG=true deno task start [command]`
3. Verify your configuration with: `deno task setup`
4. Review the troubleshooting section above

## Advanced Configuration

For advanced users, you can customize:

- Gemini model parameters in `.env`
- Processing batch sizes
- Output formats and locations
- Logging levels and formats

See the main README.md for detailed configuration options.
