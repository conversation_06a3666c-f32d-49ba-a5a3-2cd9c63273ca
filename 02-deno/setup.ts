#!/usr/bin/env -S deno run --allow-all

import { ensureDir } from '@std/fs/mod.ts';
import { join } from '@std/path/mod.ts';

async function setupDirectories() {
  const directories = [
    'output',
    'output/analysis',
    'output/extracted',
    'output/combined',
    'temp',
    'logs',
    'pdf'
  ];

  console.log('🏗️  Setting up project directories...\n');

  for (const dir of directories) {
    try {
      await ensureDir(dir);
      console.log(`✅ Created directory: ${dir}`);
    } catch (error) {
      console.log(`❌ Failed to create directory ${dir}:`, error);
    }
  }

  // Copy PDF file if it exists in parent directory
  try {
    const sourcePdf = '../00-pdf/list.pdf';
    const targetPdf = 'pdf/list.pdf';

    try {
      await Deno.stat(sourcePdf);
      await Deno.copyFile(sourcePdf, targetPdf);
      console.log(`✅ Copied PDF file to: ${targetPdf}`);
    } catch {
      console.log(`ℹ️  PDF file not found at ${sourcePdf}, please add your PDF to pdf/list.pdf`);
    }
  } catch (error) {
    console.log('ℹ️  Could not copy PDF file:', error);
  }

  // Create .env file if it doesn't exist
  try {
    await Deno.stat('.env');
    console.log('ℹ️  .env file already exists');
  } catch {
    try {
      await Deno.copyFile('.env.example', '.env');
      console.log('✅ Created .env file from .env.example');
      console.log('⚠️  Please edit .env file and add your GEMINI_API_KEY');
    } catch (error) {
      console.log('❌ Failed to create .env file:', error);
    }
  }

  console.log('\n🎉 Setup completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Edit .env file and add your GEMINI_API_KEY');
  console.log('2. Place your PDF file in pdf/list.pdf');
  console.log('3. Run: deno task setup');
  console.log('4. Run: deno task start full');
}

if (import.meta.main) {
  await setupDirectories();
}
