{"name": "@guide-ocr/deno", "version": "1.0.0", "description": "TypeScript/Deno application for OCR processing of PDF files with Google Gemini AI", "type": "module", "main": "src/main.ts", "scripts": {"setup-dirs": "deno run --allow-all setup.ts", "start": "deno run --allow-all src/main.ts", "dev": "deno run --allow-all --watch src/main.ts", "analyze": "deno run --allow-all src/main.ts analyze", "extract": "deno run --allow-all src/main.ts extract", "convert": "deno run --allow-all src/main.ts convert", "combine": "deno run --allow-all src/main.ts combine", "setup": "deno run --allow-all src/main.ts setup", "clean": "deno run --allow-all src/main.ts clean", "lint": "deno lint src/", "fmt": "deno fmt src/", "check": "deno check **/*.ts", "test": "deno test --allow-all src/", "test-setup": "deno run --allow-all test-setup.ts"}, "keywords": ["ocr", "pdf", "gemini", "typescript", "deno", "excel", "business-listings"], "author": "", "license": "MIT", "engines": {"deno": ">=1.40.0"}, "dependencies": {"@google/generative-ai": "^0.24.1", "exceljs": "^4.4.0", "sharp": "^0.34.2", "commander": "^14.0.0"}}